levels:
  watch: 
    value: 20008
    colour: white
  watchdebug: 
    value: 20007
    colour: white
  binge: 
    value: 20006
    colour: white
  bingedebug: 
    value: 20005
    colour: cyan
  advanceddebug: 
    value: 20004
    colour: cyan
  advanced: 
    value: 20003
    colour: cyan
  scdebug: 
    value: 20002
    colour: cyan
  sc: 
    value: 20001
    colour: cyan

appenders:
  console:
    type: console
    layout:
      type: pattern
      pattern: '%[[%d{ISO8601}][%p %c] %z (%f{3} %l:%o)%] %m'
  trace:
    type: dateFile
    filename: logs/app-access.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  http:
    type: logLevelFilter
    appender: trace
    level: trace
    maxLevel: trace
  info:
    type: dateFile
    filename: logs/app-info.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  advanced:
    type: dateFile
    filename: logs/app-advanced.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  advanceddebug:
    type: dateFile
    filename: logs/app-advanced-debug.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  watch:
    type: dateFile
    filename: logs/app-watch.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  watchdebug:
    type: dateFile
    filename: logs/app-watch-debug.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  binge:
    type: dateFile
    filename: logs/app-binge.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  bingedebug:
    type: dateFile
    filename: logs/app-binge-debug.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  sc:
    type: dateFile
    filename: logs/app-sc.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  scdebug:
    type: dateFile
    filename: logs/app-sc-debug.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  debug:
    type: dateFile
    filename: logs/app-debug.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '[%d{ISO8601}][%p %c] %z (%f{3} %l:%o) %m'
  infoFilter:
    type: logLevelFilter
    appender: info
    level: info
    maxLevel: info
  advancedFilter:
    type: logLevelFilter
    appender: advanced
    level: advanced
    maxLevel: advanced
  advanceddebugFilter:
    type: logLevelFilter
    appender: advanceddebug
    level: advanceddebug
    maxLevel: advanceddebug
  watchFilter:
    type: logLevelFilter
    appender: watch
    level: watch
    maxLevel: watch
  watchdebugFilter:
    type: logLevelFilter
    appender: watchdebug
    level: watchdebug
    maxLevel: watchdebug
  bingeFilter:
    type: logLevelFilter
    appender: binge
    level: binge
    maxLevel: binge
  bingedebugFilter:
    type: logLevelFilter
    appender: bingedebug
    level: bingedebug
    maxLevel: bingedebug
  scFilter:
    type: logLevelFilter
    appender: sc
    level: sc
    maxLevel: sc
  scdebugFilter:
    type: logLevelFilter
    appender: scdebug
    level: scdebug
    maxLevel: scdebug
  debugFilter:
    type: logLevelFilter
    appender: debug
    level: debug
    maxLevel: debug
  error:
    type: dateFile
    filename: logs/app-error.log
    pattern: yyyy-MM-dd
    compress: true
    numBackups: 30
    layout:
      type: pattern
      pattern: '%x{redis}[%d{ISO8601}[%p %c] %z (%f{3} %l:%o) %m'
  minError:
    type: logLevelFilter
    appender: error
    level: error
categories:
  default:
    enableCallStack: true
    appenders:
      - console
      - http
      - infoFilter
      - debugFilter
      - minError
      - bingeFilter
      - bingedebugFilter
      - advancedFilter
      - advanceddebugFilter
      - watchFilter
      - watchdebugFilter
      - scFilter
      - scdebugFilter
    level: info