const logger = require('../libs/logger');
const SiteMod = require('../model/SiteMod');

const siteMod = new SiteMod();

class Site {
  async add (req, res) {
    const options = req.body;
    try {
      const r = siteMod.add(options);
      res.send({
        success: true,
        message: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async delete (req, res) {
    const options = req.body;
    try {
      const r = siteMod.delete(options);
      res.send({
        success: true,
        message: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async modify (req, res) {
    const options = req.body;
    try {
      const r = siteMod.modify(options);
      res.send({
        success: true,
        message: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async list (req, res) {
    const options = req.body;
    try {
      const r = await siteMod.list(options);
      res.send({
        success: true,
        data: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async listRecord (req, res) {
    const options = req.body;
    try {
      const r = await siteMod.listRecord(options);
      res.send({
        success: true,
        data: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async refresh (req, res) {
    const options = req.query;
    try {
      const r = await siteMod.refresh(options);
      res.send({
        success: true,
        message: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async search (req, res) {
    const options = req.query;
    try {
      const r = await siteMod.search(options);
      res.send({
        success: true,
        data: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async pushTorrent (req, res) {
    const options = req.body;
    try {
      const r = await siteMod.pushTorrent(options);
      res.send({
        success: true,
        message: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async listSite (req, res) {
    const options = req.query;
    try {
      const r = await siteMod.listSite(options);
      res.send({
        success: true,
        data: r
      });
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };

  async overview (req, res) {
    try {
      const image = await siteMod.overview(req.query);
      res.setHeader('content-type', 'image/jpeg');
      res.send(image);
    } catch (e) {
      logger.error(e);
      res.send({
        success: false,
        message: e.message
      });
    }
  };
}
module.exports = Site;
