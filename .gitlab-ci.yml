stages:
  - generate_static
  - build_docker_image_and_push

generate_static:
  image: alpine:3.14
  before_script:
    - apk update
    - apk add nodejs npm git coreutils curl tzdata
  stage: generate_static
  tags:
    - compile
  script:
    - CI_COMMIT_BRANCH=`[ $CI_COMMIT_TAG ] && echo $CI_COMMIT_TAG || echo $CI_COMMIT_BRANCH`
    - echo $CI_COMMIT_BRANCH
    - PUSH_TIME=$(TZ=Asia/Shanghai date -d $CI_COMMIT_TIMESTAMP "+%Y-%m-%d %H:%M:%S")
    - COMMIT_MESSAGE=`echo $CI_COMMIT_MESSAGE | sed 's/\n//g'`
    - |
      curl --location --request POST 'https://vertex-push.lswl.workers.dev' \
      --header 'Content-Type: application/json' \
      --data-raw "{\"key\": \"$KEY\",\"time\": \"$PUSH_TIME\",\"message\": \"$COMMIT_MESSAGE\",\"branch\": \"$CI_COMMIT_BRANCH\",\"sha1\": \"${CI_COMMIT_SHA:0:12}\", \"type\": \"start\"}"
    - cd webui
    - npm i --legacy-peer-deps
    - mkdir ./public/assets/styles
    - node dark
    - node light
    - node cyber
    - echo '@import url(/api/setting/getBackground.less);' >> ./public/assets/styles/cyber.less
    - echo '.body-bg {' >> ./public/assets/styles/cyber.less
    - echo '  background:@vt-bg-image;' >> ./public/assets/styles/cyber.less
    - echo '  background-position-x:center;' >> ./public/assets/styles/cyber.less
    - echo '  background-position-y:center;' >> ./public/assets/styles/cyber.less
    - echo '  background-size:cover;' >> ./public/assets/styles/cyber.less
    - echo '}' >> ./public/assets/styles/cyber.less
    - echo '' >> ./public/assets/styles/cyber.less
    - echo '.login-layout {' >> ./public/assets/styles/cyber.less
    - echo '  background:@body-background;' >> ./public/assets/styles/cyber.less
    - echo '}' >> ./public/assets/styles/cyber.less
    - echo '' > ./public/assets/styles/follow.less
    - npm run build
    - cd ../app
    - tar -czvf ../static.tar.gz static
  artifacts:
    expire_in: 100 mins
    paths: 
      - static.tar.gz


build_docker_image_and_push:
  image: docker:20.10
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_DRIVER: overlay2
    IMAGE_TAG_LATEST: lswl/vertex:latest
    DOCKER_BUILDKIT: 1
  services:
    - docker:20.10-dind
  before_script:
    - apk update
    - apk add tzdata curl coreutils
    - docker info
    - CI_COMMIT_BRANCH=`[ $CI_COMMIT_TAG ] && echo $CI_COMMIT_TAG || echo $CI_COMMIT_BRANCH`
    - IMAGE_TAG_BRANCH=lswl/vertex:$CI_COMMIT_BRANCH
    - echo $IMAGE_TAG_SHA
    - echo $IMAGE_TAG_LATEST
    - echo $IMAGE_TAG_BRANCH
    - TIME=$(TZ=Asia/Shanghai date -d $CI_COMMIT_TIMESTAMP "+%Y_%m_%d_%H_%M_%S")
    - PUSH_TIME=$(TZ=Asia/Shanghai date -d $CI_COMMIT_TIMESTAMP "+%Y-%m-%d %H:%M:%S")
    - echo $TIME
    - COMMIT_MESSAGE=`echo $CI_COMMIT_MESSAGE | sed 's/\n//g'`
    - docker run --privileged --rm tonistiigi/binfmt --install all
    - mkdir -p ~/.docker/cli-plugins
    - wget "https://minio.lswl.in/share/buildx-v0.7.1.linux-arm64" -qO ~/.docker/cli-plugins/docker-buildx
    - chmod +x ~/.docker/cli-plugins/docker-buildx
    - docker context create context_name
    - docker buildx create --name buildx_name --use context_name
    - docker buildx inspect --bootstrap
  stage: build_docker_image_and_push
  tags: 
    - compile
  script:
    - docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD
    - mkdir docker/src
    - tar -xzvf static.tar.gz -C docker/src/
    - cd docker
    - sed -i 's/COMMIT_ID/'$CI_COMMIT_SHA'/' Dockerfile
    - ls src/static
    - docker pull --platform linux/arm64 lswl/vertex:latest || echo 'fail'
    - docker pull --platform linux/amd64 lswl/vertex:latest || echo 'fail'
    - |
      docker buildx build --platform linux/amd64,linux/arm64 --cache-from lswl/vertex --tag $IMAGE_TAG_LATEST --build-arg BUILDKIT_INLINE_CACHE=1 . --push
    - |
      docker buildx build --platform linux/amd64,linux/arm64 --cache-from lswl/vertex --tag $IMAGE_TAG_BRANCH --build-arg BUILDKIT_INLINE_CACHE=1 . --push
    - |
      curl --location --request POST 'https://vertex-push.lswl.workers.dev' \
      --header 'Content-Type: application/json' \
      --data-raw "{\"key\": \"$KEY\",\"time\": \"$PUSH_TIME\",\"message\": \"$COMMIT_MESSAGE\",\"branch\": \"$CI_COMMIT_BRANCH\",\"sha1\": \"${CI_COMMIT_SHA:0:12}\"}"